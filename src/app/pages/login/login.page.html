<ion-content [fullscreen]="true">
    <div class="rounded-lg bg-gray-100 shadow-lg  min-h-screen p-4">
        <!-- Company Info & Welcome Section -->
        <div class="bg-white flex flex-row w-1/2 m-10 justify-center items-center rounded-lg p-4">
            <div class="hidden md:flex flex-col justify-center items-center p-8 text-black ">
                <div class="flex justify-center w-full p-3">
                    <img style="width: 140px;"
                        src="https://djpw4cfh60y52.cloudfront.net/uploads/logo/ljD8TdmW3xUCdu1eHw8Gd31UIsMrYpt6DVHm3kRy.jpg"
                        alt="Logo" />
                </div>
                <h1 class="text-4xl font-bold mb-4">Welcome Back!</h1>
                <p class="text-xl text-center mb-6">Sign in to access your POS dashboard</p>

            </div>

            <div class="flex items-center justify-center ">
                <div class="w-full max-w-md ">
                    <h5 class="text-xl  text-center mb-6">Login</h5>
                    <form class="space-y-5" (ngSubmit)="onLogin()">
                        <div>
                            <label class="block text-gray-700 mb-1" for="username">Username</label>
                            <input id="username" type="text" placeholder="Enter your username" pInputText
                                [(ngModel)]="username" name="username"
                                class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-1" for="password">Password</label>
                            <input id="password" type="password" placeholder="Enter your password" pInputText
                                [(ngModel)]="password" name="password"
                                class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                        </div>
                        <div class="flex justify-end mt-1">
                            <a href="#" class="text-sm text-blue-500 hover:text-blue-700">Forgot Password?</a>
                        </div>
                        <button type="submit" (click)="onLogin()" (keyup.enter)="onLogin()" label="Login"
                            class="w-full mt-6 bg-blue-700 text-white font-bold rounded-md px-4 py-2 hover:bg-blue-800">
                            Login
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</ion-content>